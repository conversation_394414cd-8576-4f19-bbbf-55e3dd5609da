<template>
  <div class="login-container">
    <div class="login-left-box">
      <img src="../assets/copywriter.png" alt="Spacetime&AIOS" class="main-logo" />
    </div>
    <div class="login-right-box">
      <img src="../assets/logo.svg" alt="logo" class="logo" />
      <div class="welcome-text">
        <span class="hello-text">hello!</span>
        <span class="welcome-title">欢迎登录热爱元宇宙</span>
      </div>

      <el-form :model="loginForm" class="login-form">
        <el-form-item>
          <el-input
            v-model="loginForm.phone"
            placeholder="手机号"
            class="form-input"
            size="large"
          >
          </el-input>
        </el-form-item>
        <el-form-item v-if="isPasswordLogin">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            class="form-input"
            size="large"
            show-password
          />
        </el-form-item>
        <el-form-item v-else>
          <el-input
            v-model="loginForm.verifyCode"
            placeholder="验证码"
            class="form-input verify-code-input"
            size="large"
          >
            <template #suffix>
              <el-button
                type="text"
                class="send-code-btn"
                @click="sendVerifyCode"
              >
                发送验证码
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            class="login-btn"
            size="large"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>

      <div class="footer-links">
        <span class="link-text">没有账号？<span class="register-link" @click="goToRegister">去注册</span></span>
        <span class="link-text" v-if="isPasswordLogin" @click="geToForgetPassword">忘记密码？</span>
      </div>

      <div class="other-login">
        <div class="divider">
          <span class="divider-text">其他登录方式</span>
        </div>
        <div class="login-icons">
          <img
            :src="isPasswordLogin ? '/src/assets/login/phone-login.svg' : '/src/assets/login/password_login.svg'"
            :alt="isPasswordLogin ? '手机登录' : '密码登录'"
            class="login-icon"
            @click="toggleLoginMode"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElForm, ElFormItem, ElInput, ElButton, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()
// 登录方式：true为密码登录，false为验证码登录
const isPasswordLogin = ref(true)

const loginForm = reactive({
  phone: '',
  password: '',
  verifyCode: ''
})

const handleLogin = () => {
  if (!loginForm.phone) {
    ElMessage.warning('请输入手机号')
    return
  }

  if (isPasswordLogin.value) {
    if (!loginForm.password) {
      ElMessage.warning('请输入密码')
      return
    }
  } else {
    if (!loginForm.verifyCode) {
      ElMessage.warning('请输入验证码')
      return
    }
  }

  // 这里可以添加实际的登录逻辑
  console.log('登录信息:', loginForm)
  console.log('登录方式:', isPasswordLogin.value ? '密码登录' : '验证码登录')
  ElMessage.success('登录成功！')
}

// 切换登录方式
const toggleLoginMode = () => {
  isPasswordLogin.value = !isPasswordLogin.value
  // 清空相关输入框
  if (isPasswordLogin.value) {
    loginForm.verifyCode = ''
  } else {
    loginForm.password = ''
  }
}

// 发送验证码
const sendVerifyCode = () => {
  if (!loginForm.phone) {
    ElMessage.warning('请先输入手机号')
    return
  }

  // 这里可以添加实际的发送验证码逻辑
  console.log('发送验证码到:', loginForm.phone)
  ElMessage.success('验证码已发送！')
}

// 去注册页面
const goToRegister = () => {
  router.push('/register')
}

//去忘记密码页面
const geToForgetPassword = () => {
  router.push('/forgetPassword')
}
</script>

<style scoped>
.login-container {
  margin: 10px;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  display: flex;
  flex-direction: row;
}

.login-left-box {
  background-image: url('../assets/banner.png');
  width: 1184px;
  height: 940px;
  background-size: cover;
  background-position: center;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

.main-logo {
  width: 722px;
  height: 338px;
}

.login-right-box {
  display: flex;
  width: 706px;
  padding: 40px;
  flex-direction: column;
  flex-shrink: 0;
  align-self: stretch;
  border-radius: 20px;
  background: #ffffff;
}

.logo {
  height: 45px;
  width: auto;
  align-self: flex-start;
  margin-bottom: 60px;
}

.welcome-text {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}

.hello-text {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 40px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  margin-bottom: 8px;
}

.welcome-title {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 40px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
}

.login-form {
  width: 100%;
}

.form-input {
  width: 100%;
  margin-bottom: 20px;
}

:deep(.form-input .el-input__wrapper) {
  background-color: #f2f3f5;
  border: 1px solid #E5E6EB;
  border-radius: 10px;
  padding: 0px 16px;
  height: 56px;
  box-shadow: none;
}

:deep(.form-input .el-input__wrapper.is-focus) {
  border-color: #2F7DFB;
  box-shadow: 0 0 0 2px rgba(47, 125, 251, 0.1);
}

:deep(.form-input .el-input__inner) {
  background: transparent;
  border: none;
  font-size: 16px;
  color: #1D2129;
}

:deep(.form-input .el-input__inner::placeholder) {
  color: #86909C;
}

/* 验证码输入框样式 */
.verify-code-input {
  position: relative;
}

:deep(.verify-code-input .el-input__suffix) {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  padding-right: 16px;
}

.send-code-btn {
  color: #2F7DFB;
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 0;
  height: auto;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.send-code-btn:hover {
  opacity: 0.8;
}

:deep(.send-code-btn.is-disabled) {
  color: #86909C;
  cursor: not-allowed;
}

.login-btn {
  width: 100%;
  height: 60px;
  border-radius: 10px;
  font-size: 18px;
  font-family: "江城斜黑体", sans-serif;
  border: 1px solid rgba(255, 255, 255, 0.20);
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%), linear-gradient(0deg, #2F7DFB 0%, #2F7DFB 100%), linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  color: #FFF;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 1.8px;
}

.footer-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 100px;
}

.link-text {
  color: var(--text-3, #86909C);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
  transition: color 0.3s ease;
}

.register-link {
  color: var(--primary, #2F7DFB);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
  transition: color 0.3s ease;
}

.register-link:hover {
  opacity: 0.8;
}

.other-login {
  width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  align-self: center;
  margin: 0 auto;
}

.divider {
  width: 100%;
  position: relative;
  text-align: center;
  margin-bottom: 30px;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #E5E6EB;
  z-index: 1;
}

.divider-text {
  background: #ffffff;
  padding: 0 20px;
  color: var(--text-3, #86909C);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  position: relative;
  z-index: 2;
}

.login-icons {
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-icon {
  width: 48px;
  height: 48px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.login-icon:hover {
  transform: scale(1.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    margin: 0;
    width: 100%;
    height: 100vh;
  }

  .login-left-box {
    width: 100%;
    height: 40vh;
    min-height: 300px;
    border-radius: 0 0 20px 20px;
  }

  .main-logo {
    width: 60vw;
    height: auto;
    max-width: 400px;
  }

  .login-right-box {
    width: 100%;
    height: 60vh;
    padding: 30px 20px;
    border-radius: 20px 20px 0 0;
    margin-top: -20px;
    z-index: 10;
    position: relative;
  }

  .logo {
    height: 35px;
    margin-bottom: 30px;
  }

  .hello-text,
  .welcome-title {
    font-size: 28px;
  }

  .welcome-text {
    margin-bottom: 30px;
  }

  :deep(.form-input .el-input__wrapper) {
    height: 50px;
    padding: 10px 14px;
  }

  .login-btn {
    height: 50px;
    font-size: 16px;
  }

  .form-input {
    width: 100%;
  }

  .footer-links {
    margin-top: 15px;
    margin-bottom: 50px;
  }

  .link-text {
    font-size: 13px;
  }

  .other-login {
    width: 100%;
  }
}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
  .login-right-box {
    padding: 20px 16px;
  }

  .hello-text,
  .welcome-title {
    font-size: 24px;
  }

  .welcome-text {
    margin-bottom: 25px;
  }

  :deep(.form-input .el-input__wrapper) {
    height: 48px;
    padding: 8px 12px;
  }

  :deep(.form-input .el-input__inner) {
    font-size: 15px;
  }

  .login-btn {
    height: 48px;
    font-size: 15px;
  }

  .form-input {
    width: 100%;
  }

  .other-login {
    width: 100%;
  }
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei500W.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei900W.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

</style>